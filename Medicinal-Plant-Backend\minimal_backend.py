
from flask import Flask, request, jsonify
from flask_cors import CORS
import random

app = Flask(__name__)
CORS(app)

PLANTS = {
    "Tulsi": {"scientific": "Ocimum tenuiflorum", "use": "Respiratory health"},
    "Turmeric": {"scientific": "Curcuma longa", "use": "Anti-inflammatory"},
    "Neem": {"scientific": "Azadirachta indica", "use": "Antibacterial"},
    "Aloe Vera": {"scientific": "Aloe vera", "use": "Skin healing"},
    "Mint": {"scientific": "Mentha spicata", "use": "Digestive aid"},
    "Ginger": {"scientific": "Zingiber officinale", "use": "Anti-nausea"},
    "Ajwain": {"scientific": "Trachyspermum ammi", "use": "Digestive disorders"}
}

@app.route("/")
def home():
    return jsonify({"message": "API Running", "plants": len(PLANTS)})

@app.route("/health")
def health():
    return jsonify({"status": "healthy", "plants": len(PLANTS)})

@app.route("/predict", methods=["POST"])
def predict():
    try:
        if "image" not in request.files and "file" not in request.files:
            return jsonify({"error": "No file"}), 400
        
        # Random prediction for demo
        plant_names = list(PLANTS.keys())
        plant_name = random.choice(plant_names)
        plant_data = PLANTS[plant_name]
        confidence = random.uniform(75, 95)
        
        result = {
            "plantName": plant_name,
            "scientificName": plant_data["scientific"],
            "localName": plant_name,
            "confidence": round(confidence, 1),
            "primaryMedicinalUse": plant_data["use"],
            "overview": {"appearance": f"{plant_name} plant"},
            "traditionalUse": {"ayurveda": {"uses": [plant_data["use"]]}},
            "preparation": [{"method": "Traditional", "preparation": "As needed"}],
            "safety": {"toxicity_level": "Generally safe"},
            "geography": {"native_regions": ["India"]},
            "medicinalFeature": [plant_data["use"]],
            "label": plant_data["scientific"].lower().replace(" ", "_")
        }
        
        print(f"Predicted: {plant_name} ({confidence:.1f}%)")
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    print("🚀 Backend starting on http://localhost:5000")
    app.run(debug=False, host="127.0.0.1", port=5000)
