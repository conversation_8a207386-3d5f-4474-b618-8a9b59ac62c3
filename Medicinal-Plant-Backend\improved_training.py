#!/usr/bin/env python3
"""
Improved Training Script for Medicinal Plant Recognition
Uses advanced techniques for better accuracy and generalization
"""
import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import tensorflow as tf
from tensorflow.keras import layers, models, callbacks, optimizers
from tensorflow.keras.applications import EfficientNetB0, MobileNetV3Large, ResNet50V2
from sklearn.metrics import classification_report, confusion_matrix
import argparse

class PlantModelTrainer:
    def __init__(self, data_dir="data/plants_dataset", model_dir="models"):
        self.data_dir = Path(data_dir)
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(exist_ok=True)
        
        self.train_dir = self.data_dir / "train"
        self.val_dir = self.data_dir / "val"
        self.test_dir = self.data_dir / "test"
        
        self.img_size = 224
        self.batch_size = 16
        self.model = None
        self.class_names = None
        
    def prepare_datasets(self):
        """Prepare training, validation, and test datasets with augmentation"""
        print("🔄 Preparing datasets...")
        
        # Data augmentation for training
        train_datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rescale=1./255,
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            fill_mode='nearest',
            brightness_range=[0.8, 1.2]
        )
        
        # Only rescaling for validation and test
        val_test_datagen = tf.keras.preprocessing.image.ImageDataGenerator(rescale=1./255)
        
        # Create datasets
        self.train_ds = train_datagen.flow_from_directory(
            self.train_dir,
            target_size=(self.img_size, self.img_size),
            batch_size=self.batch_size,
            class_mode='categorical',
            shuffle=True
        )
        
        self.val_ds = val_test_datagen.flow_from_directory(
            self.val_dir,
            target_size=(self.img_size, self.img_size),
            batch_size=self.batch_size,
            class_mode='categorical',
            shuffle=False
        )
        
        self.test_ds = val_test_datagen.flow_from_directory(
            self.test_dir,
            target_size=(self.img_size, self.img_size),
            batch_size=self.batch_size,
            class_mode='categorical',
            shuffle=False
        )
        
        self.class_names = list(self.train_ds.class_indices.keys())
        self.num_classes = len(self.class_names)
        
        print(f"✅ Dataset prepared:")
        print(f"   📊 Classes: {self.num_classes}")
        print(f"   🏋️ Training samples: {self.train_ds.samples}")
        print(f"   🔍 Validation samples: {self.val_ds.samples}")
        print(f"   🧪 Test samples: {self.test_ds.samples}")
        
        return self.train_ds, self.val_ds, self.test_ds
    
    def create_model(self, architecture="efficientnet", fine_tune=True):
        """Create model with specified architecture"""
        print(f"🏗️ Creating model with {architecture} architecture...")
        
        # Choose base model
        if architecture == "efficientnet":
            base_model = EfficientNetB0(
                weights='imagenet',
                include_top=False,
                input_shape=(self.img_size, self.img_size, 3)
            )
            preprocess_input = tf.keras.applications.efficientnet.preprocess_input
        elif architecture == "mobilenet":
            base_model = MobileNetV3Large(
                weights='imagenet',
                include_top=False,
                input_shape=(self.img_size, self.img_size, 3)
            )
            preprocess_input = tf.keras.applications.mobilenet_v3.preprocess_input
        elif architecture == "resnet":
            base_model = ResNet50V2(
                weights='imagenet',
                include_top=False,
                input_shape=(self.img_size, self.img_size, 3)
            )
            preprocess_input = tf.keras.applications.resnet_v2.preprocess_input
        else:
            raise ValueError(f"Unsupported architecture: {architecture}")
        
        # Freeze base model initially
        base_model.trainable = False
        
        # Create full model
        inputs = tf.keras.Input(shape=(self.img_size, self.img_size, 3))
        x = preprocess_input(inputs)
        x = base_model(x, training=False)
        x = layers.GlobalAveragePooling2D()(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.3)(x)
        x = layers.Dense(512, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        outputs = layers.Dense(self.num_classes, activation='softmax')(x)
        
        self.model = models.Model(inputs, outputs)
        
        # Compile model
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_3_accuracy']
        )
        
        print(f"✅ Model created with {self.model.count_params():,} parameters")
        return self.model
    
    def train_model(self, epochs=50, fine_tune_epochs=20):
        """Train the model with transfer learning and fine-tuning"""
        print("🚀 Starting training...")
        
        # Callbacks
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            callbacks.ModelCheckpoint(
                filepath=self.model_dir / 'best_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # Phase 1: Train with frozen base model
        print("📚 Phase 1: Training with frozen base model...")
        history1 = self.model.fit(
            self.train_ds,
            validation_data=self.val_ds,
            epochs=epochs,
            callbacks=callbacks_list,
            verbose=1
        )
        
        # Phase 2: Fine-tuning
        print("🔧 Phase 2: Fine-tuning...")
        
        # Unfreeze base model
        self.model.layers[2].trainable = True  # base_model is at index 2
        
        # Use lower learning rate for fine-tuning
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=0.0001),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_3_accuracy']
        )
        
        history2 = self.model.fit(
            self.train_ds,
            validation_data=self.val_ds,
            epochs=fine_tune_epochs,
            callbacks=callbacks_list,
            verbose=1
        )
        
        # Combine histories
        history = {
            'accuracy': history1.history['accuracy'] + history2.history['accuracy'],
            'val_accuracy': history1.history['val_accuracy'] + history2.history['val_accuracy'],
            'loss': history1.history['loss'] + history2.history['loss'],
            'val_loss': history1.history['val_loss'] + history2.history['val_loss']
        }
        
        return history
    
    def evaluate_model(self):
        """Evaluate model on test set and generate detailed metrics"""
        print("📊 Evaluating model...")
        
        # Load best model
        self.model.load_weights(self.model_dir / 'best_model.h5')
        
        # Evaluate on test set
        test_loss, test_accuracy, test_top3_accuracy = self.model.evaluate(self.test_ds, verbose=0)
        
        print(f"✅ Test Results:")
        print(f"   🎯 Accuracy: {test_accuracy:.4f}")
        print(f"   🏆 Top-3 Accuracy: {test_top3_accuracy:.4f}")
        print(f"   📉 Loss: {test_loss:.4f}")
        
        # Generate predictions for confusion matrix
        predictions = self.model.predict(self.test_ds)
        predicted_classes = np.argmax(predictions, axis=1)
        true_classes = self.test_ds.classes
        
        # Classification report
        report = classification_report(
            true_classes, 
            predicted_classes, 
            target_names=self.class_names,
            output_dict=True
        )
        
        # Save detailed results
        results = {
            "test_accuracy": float(test_accuracy),
            "test_top3_accuracy": float(test_top3_accuracy),
            "test_loss": float(test_loss),
            "classification_report": report,
            "class_names": self.class_names
        }
        
        with open(self.model_dir / 'evaluation_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        # Plot confusion matrix
        self._plot_confusion_matrix(true_classes, predicted_classes)
        
        return results
    
    def _plot_confusion_matrix(self, true_classes, predicted_classes):
        """Plot and save confusion matrix"""
        cm = confusion_matrix(true_classes, predicted_classes)
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names)
        plt.title('Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(self.model_dir / 'confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Confusion matrix saved to confusion_matrix.png")
    
    def save_model(self, model_name="plant_classifier"):
        """Save the final model and class information"""
        # Save model
        model_path = self.model_dir / f"{model_name}.h5"
        self.model.save(model_path)
        
        # Save class information with enhanced metadata
        classes_info = {}
        for i, class_name in enumerate(self.class_names):
            classes_info[str(i)] = {
                "class_name": class_name,
                "index": i,
                "display_name": class_name.replace("_", " ").title()
            }
        
        classes_path = self.model_dir / f"{model_name}_classes.json"
        with open(classes_path, 'w') as f:
            json.dump(classes_info, f, indent=2)
        
        print(f"✅ Model saved:")
        print(f"   🤖 Model: {model_path}")
        print(f"   📋 Classes: {classes_path}")
        
        return model_path, classes_path

def main():
    parser = argparse.ArgumentParser(description="Train Medicinal Plant Recognition Model")
    parser.add_argument("--data-dir", default="data/plants_dataset", 
                       help="Path to dataset directory")
    parser.add_argument("--model-dir", default="models", 
                       help="Directory to save models")
    parser.add_argument("--architecture", choices=["efficientnet", "mobilenet", "resnet"],
                       default="efficientnet", help="Model architecture")
    parser.add_argument("--epochs", type=int, default=50, 
                       help="Number of initial training epochs")
    parser.add_argument("--fine-tune-epochs", type=int, default=20,
                       help="Number of fine-tuning epochs")
    parser.add_argument("--model-name", default="plant_classifier",
                       help="Name for saved model")
    
    args = parser.parse_args()
    
    # Create trainer
    trainer = PlantModelTrainer(args.data_dir, args.model_dir)
    
    # Prepare datasets
    trainer.prepare_datasets()
    
    # Create model
    trainer.create_model(args.architecture)
    
    # Train model
    history = trainer.train_model(args.epochs, args.fine_tune_epochs)
    
    # Evaluate model
    results = trainer.evaluate_model()
    
    # Save final model
    trainer.save_model(args.model_name)
    
    print("\n🎉 Training completed successfully!")
    print(f"Final test accuracy: {results['test_accuracy']:.4f}")

if __name__ == "__main__":
    main()
