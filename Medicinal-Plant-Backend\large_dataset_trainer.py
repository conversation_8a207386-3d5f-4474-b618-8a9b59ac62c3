#!/usr/bin/env python3
"""
Large Dataset Trainer for Medicinal Plants
Supports multiple dataset formats and provides accurate training
"""
import os
import json
import shutil
import random
from pathlib import Path
import argparse

def setup_kaggle_credentials():
    """Setup Kaggle credentials for dataset download"""
    print("🔑 Setting up Kaggle credentials...")
    print("\n📋 To download datasets from Kaggle, you need to:")
    print("1. Go to https://www.kaggle.com/account")
    print("2. Scroll to 'API' section")
    print("3. Click 'Create New API Token'")
    print("4. Download kaggle.json file")
    print("5. Place it in: C:\\Users\\<USER>\\.kaggle\\kaggle.json")
    
    kaggle_dir = Path.home() / ".kaggle"
    kaggle_file = kaggle_dir / "kaggle.json"
    
    if kaggle_file.exists():
        print(f"✅ Kaggle credentials found: {kaggle_file}")
        return True
    else:
        print(f"❌ Kaggle credentials not found: {kaggle_file}")
        print("\n🔧 Manual setup:")
        print(f"1. Create directory: {kaggle_dir}")
        print(f"2. Place kaggle.json in: {kaggle_file}")
        print("3. Run this script again")
        return False

def download_medicinal_dataset():
    """Download medicinal leaf dataset from Kaggle"""
    print("📥 Downloading medicinal leaf dataset...")
    
    try:
        import kaggle
        
        # Download the dataset
        kaggle.api.dataset_download_files(
            'iamaniket/medicinal-leaf-dataset',
            path='./datasets/',
            unzip=True
        )
        print("✅ Dataset downloaded successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download dataset: {e}")
        return False

def analyze_dataset_structure(dataset_path):
    """Analyze the structure of downloaded dataset"""
    print(f"🔍 Analyzing dataset structure: {dataset_path}")
    
    dataset_path = Path(dataset_path)
    if not dataset_path.exists():
        print(f"❌ Dataset path not found: {dataset_path}")
        return None
    
    structure = {}
    plant_classes = []
    
    # Look for common dataset structures
    for item in dataset_path.iterdir():
        if item.is_dir():
            plant_classes.append(item.name)
            image_count = len(list(item.glob("*.jpg")) + list(item.glob("*.png")) + list(item.glob("*.jpeg")))
            structure[item.name] = image_count
    
    print(f"📊 Found {len(plant_classes)} plant classes:")
    for plant, count in structure.items():
        print(f"   {plant}: {count} images")
    
    return structure

def create_comprehensive_plant_database():
    """Create comprehensive database for medicinal plants"""
    
    # Extended database with more plants commonly found in datasets
    plants_database = {
        "0": {
            "scientific_name": "Ocimum tenuiflorum",
            "local_name": "Tulsi",
            "real_name": "Holy Basil",
            "common_names": ["Tulsi", "Holy Basil", "Sacred Basil"],
            "features": [
                {"name": "Respiratory health", "usage_frequency": "very_high"},
                {"name": "Immune booster", "usage_frequency": "very_high"},
                {"name": "Stress relief", "usage_frequency": "high"},
                {"name": "Anti-inflammatory", "usage_frequency": "high"}
            ],
            "most_used_medicine": "Respiratory and immune support"
        },
        "1": {
            "scientific_name": "Curcuma longa",
            "local_name": "Haldi",
            "real_name": "Turmeric",
            "common_names": ["Turmeric", "Haldi", "Golden Spice"],
            "features": [
                {"name": "Anti-inflammatory", "usage_frequency": "very_high"},
                {"name": "Antioxidant", "usage_frequency": "very_high"},
                {"name": "Wound healing", "usage_frequency": "high"}
            ],
            "most_used_medicine": "Anti-inflammatory treatment"
        },
        "2": {
            "scientific_name": "Azadirachta indica",
            "local_name": "Neem",
            "real_name": "Indian Lilac",
            "common_names": ["Neem", "Indian Lilac", "Margosa Tree"],
            "features": [
                {"name": "Antibacterial", "usage_frequency": "very_high"},
                {"name": "Antifungal", "usage_frequency": "very_high"},
                {"name": "Skin care", "usage_frequency": "high"}
            ],
            "most_used_medicine": "Antibacterial and skin treatment"
        },
        "3": {
            "scientific_name": "Aloe barbadensis miller",
            "local_name": "Aloe Vera",
            "real_name": "True Aloe",
            "common_names": ["Aloe Vera", "Burn Plant", "True Aloe"],
            "features": [
                {"name": "Skin healing", "usage_frequency": "very_high"},
                {"name": "Digestive health", "usage_frequency": "high"},
                {"name": "Anti-inflammatory", "usage_frequency": "high"}
            ],
            "most_used_medicine": "Skin healing and digestive support"
        },
        "4": {
            "scientific_name": "Mentha spicata",
            "local_name": "Pudina",
            "real_name": "Mint",
            "common_names": ["Mint", "Pudina", "Spearmint"],
            "features": [
                {"name": "Digestive aid", "usage_frequency": "very_high"},
                {"name": "Cooling effect", "usage_frequency": "high"},
                {"name": "Respiratory relief", "usage_frequency": "high"}
            ],
            "most_used_medicine": "Digestive and cooling treatment"
        },
        "5": {
            "scientific_name": "Zingiber officinale",
            "local_name": "Adrak",
            "real_name": "Ginger",
            "common_names": ["Ginger", "Adrak", "Fresh Ginger"],
            "features": [
                {"name": "Anti-nausea", "usage_frequency": "very_high"},
                {"name": "Digestive aid", "usage_frequency": "very_high"},
                {"name": "Anti-inflammatory", "usage_frequency": "high"}
            ],
            "most_used_medicine": "Digestive and anti-nausea treatment"
        }
    }
    
    return plants_database

def organize_dataset_for_training(source_path, target_path="data/large_medicinal_dataset"):
    """Organize downloaded dataset for training"""
    print(f"🏗️ Organizing dataset from {source_path} to {target_path}")
    
    source_path = Path(source_path)
    target_path = Path(target_path)
    
    # Create target directories
    train_dir = target_path / "train"
    val_dir = target_path / "val"
    test_dir = target_path / "test"
    
    for dir_path in [train_dir, val_dir, test_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # Process each plant class
    total_images = 0
    plant_mapping = {}
    
    for class_idx, class_dir in enumerate(source_path.iterdir()):
        if not class_dir.is_dir():
            continue
            
        class_name = class_dir.name.lower().replace(" ", "_").replace("-", "_")
        plant_mapping[class_idx] = class_name
        
        # Get all images
        image_files = []
        for ext in ["*.jpg", "*.jpeg", "*.png", "*.JPG", "*.JPEG", "*.PNG"]:
            image_files.extend(list(class_dir.glob(ext)))
        
        if len(image_files) == 0:
            print(f"⚠️ No images found in {class_dir}")
            continue
        
        # Shuffle and split
        random.shuffle(image_files)
        
        train_count = int(0.7 * len(image_files))
        val_count = int(0.2 * len(image_files))
        
        train_files = image_files[:train_count]
        val_files = image_files[train_count:train_count + val_count]
        test_files = image_files[train_count + val_count:]
        
        # Create class directories
        for split_dir in [train_dir, val_dir, test_dir]:
            (split_dir / class_name).mkdir(exist_ok=True)
        
        # Copy files
        for files, split_dir in [(train_files, train_dir), (val_files, val_dir), (test_files, test_dir)]:
            for img_file in files:
                target_file = split_dir / class_name / img_file.name
                shutil.copy2(img_file, target_file)
        
        total_images += len(image_files)
        print(f"✅ {class_name}: {len(train_files)} train, {len(val_files)} val, {len(test_files)} test")
    
    print(f"🎯 Total images organized: {total_images}")
    print(f"📁 Dataset ready at: {target_path}")
    
    return plant_mapping, total_images

def create_training_script():
    """Create advanced training script for large dataset"""
    
    training_script = '''#!/usr/bin/env python3
"""
Advanced Training Script for Large Medicinal Plant Dataset
"""
import os
import json
import numpy as np
from pathlib import Path

def train_large_model(data_dir, epochs=50, batch_size=32):
    """Train model with large dataset"""
    print(f"🚀 Starting training with dataset: {data_dir}")
    
    try:
        import tensorflow as tf
        from tensorflow.keras.applications import EfficientNetB0
        from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, Dropout
        from tensorflow.keras.models import Model
        from tensorflow.keras.optimizers import Adam
        from tensorflow.keras.preprocessing.image import ImageDataGenerator
        
        # Data generators with augmentation
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=30,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        val_datagen = ImageDataGenerator(rescale=1./255)
        
        # Load data
        train_generator = train_datagen.flow_from_directory(
            f'{data_dir}/train',
            target_size=(224, 224),
            batch_size=batch_size,
            class_mode='categorical'
        )
        
        val_generator = val_datagen.flow_from_directory(
            f'{data_dir}/val',
            target_size=(224, 224),
            batch_size=batch_size,
            class_mode='categorical'
        )
        
        num_classes = train_generator.num_classes
        print(f"📊 Training with {num_classes} plant classes")
        print(f"📊 Training samples: {train_generator.samples}")
        print(f"📊 Validation samples: {val_generator.samples}")
        
        # Build model
        base_model = EfficientNetB0(weights='imagenet', include_top=False, input_shape=(224, 224, 3))
        base_model.trainable = False
        
        x = base_model.output
        x = GlobalAveragePooling2D()(x)
        x = Dropout(0.3)(x)
        x = Dense(512, activation='relu')(x)
        x = Dropout(0.3)(x)
        predictions = Dense(num_classes, activation='softmax')(x)
        
        model = Model(inputs=base_model.input, outputs=predictions)
        
        # Compile
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # Callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
            tf.keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5),
            tf.keras.callbacks.ModelCheckpoint('models/best_large_model.h5', save_best_only=True)
        ]
        
        # Train
        print("🎯 Starting training...")
        history = model.fit(
            train_generator,
            epochs=epochs,
            validation_data=val_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        # Fine-tuning
        print("🔧 Fine-tuning...")
        base_model.trainable = True
        for layer in base_model.layers[:-20]:
            layer.trainable = False
        
        model.compile(
            optimizer=Adam(learning_rate=0.0001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        history_fine = model.fit(
            train_generator,
            epochs=20,
            validation_data=val_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        # Save final model
        model.save('models/large_medicinal_model.h5')
        
        # Save class mapping
        class_mapping = {v: k for k, v in train_generator.class_indices.items()}
        with open('models/large_classes.json', 'w') as f:
            json.dump(class_mapping, f, indent=2)
        
        print("✅ Training completed successfully!")
        print(f"📊 Final validation accuracy: {max(history_fine.history['val_accuracy']):.3f}")
        
        return model, history
        
    except ImportError:
        print("❌ TensorFlow not available. Please install: pip install tensorflow")
        return None, None
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return None, None

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--data-dir', default='data/large_medicinal_dataset')
    parser.add_argument('--epochs', type=int, default=50)
    parser.add_argument('--batch-size', type=int, default=32)
    
    args = parser.parse_args()
    train_large_model(args.data_dir, args.epochs, args.batch_size)
'''
    
    with open("Medicinal-Plant-Backend/train_large_dataset.py", 'w') as f:
        f.write(training_script)
    
    print("✅ Created advanced training script: train_large_dataset.py")

def main():
    print("🌿 Large Dataset Trainer for Medicinal Plants")
    print("=" * 60)
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--setup-kaggle', action='store_true', help='Setup Kaggle credentials')
    parser.add_argument('--download', action='store_true', help='Download dataset')
    parser.add_argument('--organize', help='Organize dataset from path')
    parser.add_argument('--train', action='store_true', help='Start training')
    
    args = parser.parse_args()
    
    if args.setup_kaggle:
        setup_kaggle_credentials()
    
    elif args.download:
        if setup_kaggle_credentials():
            download_medicinal_dataset()
        
    elif args.organize:
        plant_mapping, total_images = organize_dataset_for_training(args.organize)
        print(f"🎯 Organized {total_images} images for training")
        
        # Update plant database
        plants_db = create_comprehensive_plant_database()
        
        # Save updated database
        os.makedirs("models", exist_ok=True)
        with open("models/classes.json", 'w', encoding='utf-8') as f:
            json.dump(plants_db, f, indent=2, ensure_ascii=False)
        
        print("✅ Updated plant database")
        
    elif args.train:
        create_training_script()
        print("🚀 Training script created. Run: python train_large_dataset.py")
    
    else:
        print("📋 Usage:")
        print("  --setup-kaggle    Setup Kaggle credentials")
        print("  --download        Download medicinal dataset")
        print("  --organize PATH   Organize dataset for training")
        print("  --train           Create training script")

if __name__ == "__main__":
    main()
