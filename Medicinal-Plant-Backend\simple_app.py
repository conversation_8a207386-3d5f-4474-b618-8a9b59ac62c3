#!/usr/bin/env python3
"""
Simple Medicinal Plant Recognition API - Minimal Working Version
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Simple plant database
PLANTS = {
    "0": {
        "scientific_name": "Ocimum tenuiflorum",
        "local_name": "Tulsi",
        "real_name": "Holy Basil",
        "most_used_medicine": "Respiratory support",
        "description": {"appearance": "Small aromatic herb with green leaves"},
        "traditional_systems": {"ayurveda": {"name": "Tulsi", "uses": ["Respiratory disorders"]}},
        "preparation_methods": [{"method": "Tea", "preparation": "Boil leaves in water"}],
        "safety_info": {"toxicity_level": "Generally safe"},
        "geographical_distribution": {"native_regions": ["India"]}
    },
    "1": {
        "scientific_name": "<PERSON>urcum<PERSON> longa",
        "local_name": "<PERSON><PERSON>",
        "real_name": "Turmeric",
        "most_used_medicine": "Anti-inflammatory",
        "description": {"appearance": "Yellow rhizome with orange flesh"},
        "traditional_systems": {"ayurveda": {"name": "Haridra", "uses": ["Inflammation"]}},
        "preparation_methods": [{"method": "Powder", "preparation": "Dry and grind rhizome"}],
        "safety_info": {"toxicity_level": "Generally safe"},
        "geographical_distribution": {"native_regions": ["India", "Southeast Asia"]}
    },
    "2": {
        "scientific_name": "Azadirachta indica",
        "local_name": "Neem",
        "real_name": "Neem",
        "most_used_medicine": "Antibacterial",
        "description": {"appearance": "Large tree with bitter leaves"},
        "traditional_systems": {"ayurveda": {"name": "Nimba", "uses": ["Skin disorders"]}},
        "preparation_methods": [{"method": "Oil", "preparation": "Extract oil from seeds"}],
        "safety_info": {"toxicity_level": "Safe in moderate doses"},
        "geographical_distribution": {"native_regions": ["India"]}
    },
    "3": {
        "scientific_name": "Aloe vera",
        "local_name": "Ghritkumari",
        "real_name": "Aloe Vera",
        "most_used_medicine": "Skin healing",
        "description": {"appearance": "Succulent plant with thick leaves"},
        "traditional_systems": {"ayurveda": {"name": "Ghritkumari", "uses": ["Skin healing"]}},
        "preparation_methods": [{"method": "Gel", "preparation": "Extract gel from leaves"}],
        "safety_info": {"toxicity_level": "Generally safe"},
        "geographical_distribution": {"native_regions": ["Arabian Peninsula"]}
    },
    "4": {
        "scientific_name": "Mentha spicata",
        "local_name": "Pudina",
        "real_name": "Mint",
        "most_used_medicine": "Digestive aid",
        "description": {"appearance": "Aromatic herb with serrated leaves"},
        "traditional_systems": {"ayurveda": {"name": "Pudina", "uses": ["Digestive disorders"]}},
        "preparation_methods": [{"method": "Tea", "preparation": "Steep fresh leaves"}],
        "safety_info": {"toxicity_level": "Generally safe"},
        "geographical_distribution": {"native_regions": ["Mediterranean"]}
    },
    "5": {
        "scientific_name": "Zingiber officinale",
        "local_name": "Adrak",
        "real_name": "Ginger",
        "most_used_medicine": "Anti-nausea",
        "description": {"appearance": "Knobby rhizome with fibrous texture"},
        "traditional_systems": {"ayurveda": {"name": "Ardraka", "uses": ["Digestive disorders"]}},
        "preparation_methods": [{"method": "Tea", "preparation": "Boil fresh rhizome"}],
        "safety_info": {"toxicity_level": "Generally safe"},
        "geographical_distribution": {"native_regions": ["Southeast Asia"]}
    },
    "6": {
        "scientific_name": "Trachyspermum ammi",
        "local_name": "Ajwain",
        "real_name": "Carom Seeds",
        "most_used_medicine": "Digestive disorders",
        "description": {"appearance": "Small oval seeds with strong aroma"},
        "traditional_systems": {"ayurveda": {"name": "Yavani", "uses": ["Stomach problems"]}},
        "preparation_methods": [{"method": "Water", "preparation": "Soak seeds in water"}],
        "safety_info": {"toxicity_level": "Generally safe"},
        "geographical_distribution": {"native_regions": ["India", "Middle East"]}
    }
}

def simple_prediction():
    """Simple prediction logic"""
    import random
    plant_id = str(random.randint(0, 6))
    confidence = random.uniform(75, 95)
    return plant_id, confidence

@app.route("/")
def home():
    return jsonify({
        "message": "Medicinal Plant Recognition API is running!",
        "plants": len(PLANTS),
        "status": "healthy"
    })

@app.route("/health")
def health():
    return jsonify({
        "status": "healthy",
        "plants_loaded": len(PLANTS),
        "version": "simple"
    })

@app.route("/predict", methods=["POST"])
def predict():
    """Main prediction endpoint"""
    try:
        # Check for file upload
        if "image" not in request.files and "file" not in request.files:
            return jsonify({"error": "No image file uploaded"}), 400
        
        file = request.files.get("image") or request.files.get("file")
        
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        # Simple prediction
        plant_id, confidence = simple_prediction()
        plant_data = PLANTS[plant_id]
        
        # Build response
        result = {
            "plantName": plant_data["real_name"],
            "scientificName": plant_data["scientific_name"],
            "localName": plant_data["local_name"],
            "confidence": round(confidence, 1),
            "primaryMedicinalUse": plant_data["most_used_medicine"],
            
            # Comprehensive information
            "overview": plant_data["description"],
            "traditionalUse": plant_data["traditional_systems"],
            "preparation": plant_data["preparation_methods"],
            "safety": plant_data["safety_info"],
            "geography": plant_data["geographical_distribution"],
            
            # Additional fields
            "medicinalFeature": [plant_data["most_used_medicine"]],
            "label": plant_data["scientific_name"].lower().replace(" ", "_"),
            "_simple_prediction": True
        }
        
        print(f"Prediction: {result['plantName']} ({result['confidence']}%)")
        return jsonify(result)
        
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({"error": f"Prediction failed: {str(e)}"}), 500

@app.route("/api/predict", methods=["POST"])
def api_predict():
    """API endpoint for compatibility"""
    return predict()

if __name__ == "__main__":
    print("🚀 Starting Simple Medicinal Plant API...")
    print("📍 Backend available at: http://localhost:5000")
    print(f"🌿 Ready to identify {len(PLANTS)} medicinal plants!")
    
    try:
        app.run(debug=False, host="0.0.0.0", port=5000, threaded=True)
    except Exception as e:
        print(f"Failed to start server: {e}")
        print("Trying alternative port...")
        app.run(debug=False, host="127.0.0.1", port=5001, threaded=True)
