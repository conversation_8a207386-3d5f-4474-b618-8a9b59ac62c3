#!/usr/bin/env python3
"""
Quick test of the fixed system
"""

import requests
import json
from PIL import Image
import io

def create_simple_test_image(color_rgb):
    """Create a simple test image"""
    img = Image.new('RGB', (200, 200), color_rgb)
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    return img_bytes.getvalue()

def test_prediction(color, plant_name):
    """Test a single prediction"""
    print(f"\n🧪 Testing {plant_name} with color {color}")
    
    image_bytes = create_simple_test_image(color)
    
    url = "http://localhost:5000/predict"
    files = {'file': ('test.jpg', image_bytes, 'image/jpeg')}
    
    try:
        response = requests.post(url, files=files)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Result: {result.get('plantName')} ({result.get('confidence', 0):.1f}%)")
            
            # Check if comprehensive info is available
            has_overview = bool(result.get('overview'))
            has_traditional = bool(result.get('traditionalUse'))
            has_preparation = bool(result.get('preparation'))
            has_safety = bool(result.get('safety'))
            has_geography = bool(result.get('geography'))
            
            print(f"📋 Info: Overview:{has_overview} Traditional:{has_traditional} Prep:{has_preparation} Safety:{has_safety} Geo:{has_geography}")
            
            return result
        else:
            print(f"❌ Error: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Failed: {e}")
        return None

def main():
    print("🌿 QUICK SYSTEM TEST")
    print("="*50)
    
    # Test different colors that should trigger different plants
    tests = [
        ((70, 120, 50), "Green (Tulsi-like)"),
        ((200, 150, 50), "Yellow (Turmeric-like)"),
        ((130, 120, 100), "Brown (Ajwain-like)"),
        ((50, 100, 45), "Dark Green (Neem-like)"),
        ((90, 160, 90), "Bright Green (Mint-like)")
    ]
    
    results = []
    for color, name in tests:
        result = test_prediction(color, name)
        results.append(result is not None)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 All tests passed!")
        print("✅ System is working with comprehensive information")
        print("🌐 Ready to use at: http://localhost:5173")
    else:
        print("⚠️ Some tests failed")

if __name__ == "__main__":
    main()
