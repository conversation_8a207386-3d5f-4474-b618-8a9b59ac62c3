#!/usr/bin/env python3
"""
Setup script to create a proper dataset for Tulsi and other medicinal plants
This script helps you organize your plant images for training
"""
import os
import shutil
from pathlib import Path
import json

def create_plant_dataset_structure():
    """Create the proper directory structure for plant dataset"""
    base_dir = Path("Medicinal-Plant-Backend/data/plants_dataset")
    
    # Create main directories
    directories = [
        base_dir / "train",
        base_dir / "val", 
        base_dir / "test"
    ]
    
    for dir_path in directories:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    print("✅ Created dataset directory structure")
    return base_dir

def setup_common_medicinal_plants():
    """Setup directories for common medicinal plants"""
    base_dir = create_plant_dataset_structure()
    
    # List of common medicinal plants with their scientific names
    plants = {
        "tulsi": {
            "scientific_name": "Ocimum tenuiflorum",
            "common_names": ["Holy Basil", "Sacred Basil", "Tulsi"],
            "description": "Sacred plant in Hindu tradition, excellent for respiratory health"
        },
        "turmeric": {
            "scientific_name": "<PERSON>urcum<PERSON> longa", 
            "common_names": ["Haldi", "Golden Spice"],
            "description": "Powerful anti-inflammatory and antioxidant spice"
        },
        "neem": {
            "scientific_name": "Azadirachta indica",
            "common_names": ["Indian Lilac", "Margosa Tree"],
            "description": "Natural antibacterial and antifungal properties"
        },
        "aloe_vera": {
            "scientific_name": "Aloe barbadensis miller",
            "common_names": ["Aloe", "Burn Plant"],
            "description": "Excellent for skin healing and digestive health"
        },
        "ginger": {
            "scientific_name": "Zingiber officinale",
            "common_names": ["Adrak", "Fresh Ginger"],
            "description": "Digestive aid and anti-nausea properties"
        },
        "mint": {
            "scientific_name": "Mentha spicata",
            "common_names": ["Pudina", "Spearmint"],
            "description": "Digestive and cooling properties"
        },
        "curry_leaves": {
            "scientific_name": "Murraya koenigii",
            "common_names": ["Kadi Patta", "Sweet Neem"],
            "description": "Antioxidant and blood sugar regulation"
        },
        "coriander": {
            "scientific_name": "Coriandrum sativum",
            "common_names": ["Dhania", "Cilantro"],
            "description": "Digestive and detoxifying properties"
        }
    }
    
    # Create directories for each plant
    for plant_name, info in plants.items():
        for split in ["train", "val", "test"]:
            plant_dir = base_dir / split / plant_name
            plant_dir.mkdir(exist_ok=True)
        
        # Create metadata file
        metadata_file = base_dir / f"{plant_name}_info.json"
        with open(metadata_file, 'w') as f:
            json.dump(info, f, indent=2)
    
    print(f"✅ Created directories for {len(plants)} medicinal plants")
    
    # Create instructions file
    instructions = """
# Medicinal Plant Dataset Setup Instructions

## 📸 How to Collect Images for Each Plant

### For Tulsi (Holy Basil):
1. Take photos from different angles:
   - Top view (looking down at leaves)
   - Side view (showing plant structure)
   - Close-up of individual leaves
   - Close-up of flowers (if available)
   - Different lighting conditions

2. Image requirements:
   - Minimum 50 images per plant
   - Different backgrounds
   - Various lighting conditions
   - Different growth stages
   - Clear, focused images

### Directory Structure:
```
plants_dataset/
├── train/           # 70% of images
│   ├── tulsi/
│   ├── turmeric/
│   └── ...
├── val/             # 20% of images  
│   ├── tulsi/
│   └── ...
└── test/            # 10% of images
    ├── tulsi/
    └── ...
```

## 🚀 Quick Start Commands:

1. **Add images for Tulsi:**
   ```bash
   python dataset_collector.py --action add --plant-name tulsi --source-dir /path/to/tulsi/images
   ```

2. **Augment dataset to 200 images:**
   ```bash
   python dataset_collector.py --action augment --plant-name tulsi --target-count 200
   ```

3. **Check dataset status:**
   ```bash
   python dataset_collector.py --action summary
   ```

4. **Train the model:**
   ```bash
   python improved_training.py --epochs 50 --architecture efficientnet
   ```

## 📋 Image Collection Tips:

### Good Images:
- ✅ Clear, well-lit photos
- ✅ Different angles and perspectives  
- ✅ Various backgrounds
- ✅ Different plant parts (leaves, stems, flowers)
- ✅ Natural outdoor lighting when possible

### Avoid:
- ❌ Blurry or out-of-focus images
- ❌ Too dark or overexposed photos
- ❌ Images with multiple plant species
- ❌ Heavily filtered or edited photos
- ❌ Images with text overlays

## 🎯 Target Accuracy:
With proper dataset (200+ images per plant), you should achieve:
- Training accuracy: >95%
- Validation accuracy: >90%
- Real-world accuracy: >85%
"""
    
    with open(base_dir / "INSTRUCTIONS.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Created detailed instructions file")
    return plants

def create_sample_tulsi_images():
    """Create some sample augmented images for Tulsi if you have one source image"""
    print("\n🌿 Sample Tulsi Image Generation")
    print("=" * 50)
    print("If you have Tulsi images, place them in a folder and run:")
    print("python dataset_collector.py --action add --plant-name tulsi --source-dir YOUR_TULSI_FOLDER")
    print("\nThen augment to create more training data:")
    print("python dataset_collector.py --action augment --plant-name tulsi --target-count 200")

def main():
    print("🌿 Medicinal Plant Dataset Setup")
    print("=" * 50)
    
    # Create dataset structure
    plants = setup_common_medicinal_plants()
    
    print(f"\n📁 Dataset structure created at: Medicinal-Plant-Backend/data/plants_dataset")
    print(f"🌱 Plant classes prepared: {len(plants)}")
    
    # Show next steps
    print("\n🚀 Next Steps:")
    print("1. Collect images for each plant (minimum 50 per plant)")
    print("2. Use dataset_collector.py to organize images")
    print("3. Run improved_training.py to train the model")
    print("4. Test with your Tulsi images!")
    
    print("\n📖 See INSTRUCTIONS.md for detailed guidance")
    
    # Create sample training command
    sample_commands = """
# Sample commands to get started:

# 1. Add your Tulsi images (replace path with your image folder)
python Medicinal-Plant-Backend/dataset_collector.py --action add --plant-name tulsi --source-dir /path/to/your/tulsi/images

# 2. Augment to create more training data  
python Medicinal-Plant-Backend/dataset_collector.py --action augment --plant-name tulsi --target-count 200

# 3. Check dataset status
python Medicinal-Plant-Backend/dataset_collector.py --action summary

# 4. Train the model
python Medicinal-Plant-Backend/improved_training.py --epochs 30 --architecture efficientnet

# 5. The trained model will be saved as models/plant_classifier.h5
"""
    
    with open("QUICK_START_COMMANDS.txt", 'w', encoding='utf-8') as f:
        f.write(sample_commands)
    
    print("✅ Quick start commands saved to QUICK_START_COMMANDS.txt")

if __name__ == "__main__":
    main()
