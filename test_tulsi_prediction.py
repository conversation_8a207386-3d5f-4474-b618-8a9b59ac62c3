#!/usr/bin/env python3
"""
Test script specifically for Tulsi plant identification
This will help you verify if your model correctly identifies Tulsi vs other plants
"""
import requests
import json
from PIL import Image
import io
import os
import argparse

def test_tulsi_image(image_path, backend_url="http://localhost:5000"):
    """Test a specific Tulsi image against the API"""
    
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return None
    
    print(f"🧪 Testing Tulsi image: {image_path}")
    print("=" * 60)
    
    try:
        # Read and prepare image
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # Send to API
        files = {'image': ('tulsi_test.jpg', image_data, 'image/jpeg')}
        response = requests.post(f"{backend_url}/api/predict", files=files, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            # Display results
            print("🔍 Prediction Results:")
            print(f"   🌿 Predicted Plant: {result.get('realName', 'Unknown')}")
            print(f"   🔬 Scientific Name: {result.get('scientificName', 'Unknown')}")
            print(f"   🏷️ Local Name: {result.get('localName', 'Unknown')}")
            print(f"   📊 Confidence: {result.get('confidence', 0):.1%}")
            print(f"   🎭 Mock Response: {result.get('_mock', False)}")
            
            # Check if it's correctly identified as Tulsi
            plant_name = result.get('realName', '').lower()
            scientific_name = result.get('scientificName', '').lower()
            local_name = result.get('localName', '').lower()
            
            is_tulsi = any([
                'tulsi' in plant_name,
                'holy basil' in plant_name,
                'sacred basil' in plant_name,
                'ocimum' in scientific_name,
                'tulsi' in local_name
            ])
            
            if is_tulsi:
                print("✅ CORRECT: Identified as Tulsi/Holy Basil!")
            else:
                print("❌ INCORRECT: Not identified as Tulsi")
                print("   This indicates the model needs training with Tulsi images")
            
            # Show medicinal properties
            if result.get('medicinalFeature'):
                print(f"\n💊 Medicinal Properties:")
                for feature in result.get('medicinalFeature', []):
                    print(f"   • {feature}")
            
            return result
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing image: {e}")
        return None

def analyze_prediction_accuracy(results_list):
    """Analyze accuracy across multiple test images"""
    if not results_list:
        return
    
    print("\n📊 Prediction Analysis:")
    print("=" * 40)
    
    correct_predictions = 0
    total_predictions = len(results_list)
    
    for i, result in enumerate(results_list):
        if result:
            plant_name = result.get('realName', '').lower()
            is_tulsi = 'tulsi' in plant_name or 'holy basil' in plant_name
            if is_tulsi:
                correct_predictions += 1
            print(f"Image {i+1}: {'✅' if is_tulsi else '❌'} {result.get('realName', 'Unknown')}")
    
    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    print(f"\n🎯 Accuracy: {correct_predictions}/{total_predictions} ({accuracy:.1%})")
    
    if accuracy < 0.8:
        print("\n⚠️ Low accuracy detected! Recommendations:")
        print("1. Collect more Tulsi images from different angles")
        print("2. Ensure images are clear and well-lit")
        print("3. Include different parts of the plant (leaves, stems, flowers)")
        print("4. Train the model with your collected dataset")

def create_tulsi_test_dataset():
    """Create a test dataset structure for Tulsi"""
    test_dir = "tulsi_test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    instructions = """
# Tulsi Test Images

Place your Tulsi images in this folder for testing.

## Image Guidelines:
1. Clear, focused images of Tulsi plants
2. Different angles: top view, side view, close-ups
3. Various lighting conditions
4. Different parts: leaves, stems, flowers
5. File formats: JPG, PNG, JPEG

## Testing Commands:
```bash
# Test a single image
python test_tulsi_prediction.py --image tulsi_test_images/your_image.jpg

# Test all images in folder
python test_tulsi_prediction.py --folder tulsi_test_images

# Test with custom backend URL
python test_tulsi_prediction.py --image your_image.jpg --backend http://localhost:5000
```
"""
    
    with open(f"{test_dir}/README.md", 'w') as f:
        f.write(instructions)
    
    print(f"✅ Created test directory: {test_dir}")
    print("   Place your Tulsi images there for testing")

def main():
    parser = argparse.ArgumentParser(description="Test Tulsi Plant Identification")
    parser.add_argument("--image", help="Path to single Tulsi image to test")
    parser.add_argument("--folder", help="Path to folder containing Tulsi images")
    parser.add_argument("--backend", default="http://localhost:5000", 
                       help="Backend API URL")
    parser.add_argument("--setup", action="store_true", 
                       help="Setup test directory structure")
    
    args = parser.parse_args()
    
    if args.setup:
        create_tulsi_test_dataset()
        return
    
    if not args.image and not args.folder:
        print("🌿 Tulsi Plant Identification Tester")
        print("=" * 50)
        print("Usage:")
        print("  python test_tulsi_prediction.py --image path/to/tulsi.jpg")
        print("  python test_tulsi_prediction.py --folder path/to/tulsi/images")
        print("  python test_tulsi_prediction.py --setup  # Create test directory")
        return
    
    results = []
    
    if args.image:
        # Test single image
        result = test_tulsi_image(args.image, args.backend)
        if result:
            results.append(result)
    
    elif args.folder:
        # Test all images in folder
        if not os.path.exists(args.folder):
            print(f"❌ Folder not found: {args.folder}")
            return
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        image_files = [f for f in os.listdir(args.folder) 
                      if os.path.splitext(f)[1].lower() in image_extensions]
        
        if not image_files:
            print(f"❌ No image files found in {args.folder}")
            return
        
        print(f"🧪 Testing {len(image_files)} images from {args.folder}")
        print("=" * 60)
        
        for image_file in image_files:
            image_path = os.path.join(args.folder, image_file)
            result = test_tulsi_image(image_path, args.backend)
            if result:
                results.append(result)
            print()  # Add spacing between tests
    
    # Analyze results if multiple images tested
    if len(results) > 1:
        analyze_prediction_accuracy(results)
    
    # Provide recommendations
    print("\n🚀 Next Steps:")
    if any(r.get('_mock', False) for r in results):
        print("1. The system is using mock predictions")
        print("2. Train a real model with your Tulsi dataset:")
        print("   python setup_tulsi_dataset.py")
        print("   python Medicinal-Plant-Backend/improved_training.py")
    else:
        print("1. Collect more diverse Tulsi images if accuracy is low")
        print("2. Retrain the model with additional data")
        print("3. Test with images from different sources")

if __name__ == "__main__":
    main()
