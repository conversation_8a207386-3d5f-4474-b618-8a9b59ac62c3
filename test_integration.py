#!/usr/bin/env python3
"""
Integration test for the complete Medicinal Plant Recognition system
"""
import requests
import json
from PIL import Image
import io
import time

def create_test_image():
    """Create a simple test image"""
    # Create a simple 224x224 RGB image with a plant-like pattern
    img = Image.new('RGB', (224, 224), color='green')
    
    # Add some simple patterns to make it look more like a leaf
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # Draw some leaf-like lines
    draw.line([(112, 50), (112, 174)], fill='darkgreen', width=3)  # Main vein
    draw.line([(112, 80), (80, 100)], fill='darkgreen', width=2)   # Side vein
    draw.line([(112, 100), (144, 120)], fill='darkgreen', width=2) # Side vein
    draw.line([(112, 120), (80, 140)], fill='darkgreen', width=2)  # Side vein
    draw.line([(112, 140), (144, 160)], fill='darkgreen', width=2) # Side vein
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_full_integration():
    """Test the complete system integration"""
    backend_url = "http://localhost:5000"
    frontend_url = "http://localhost:5174"
    
    print("🧪 Testing Medicinal Plant Recognition System Integration...")
    print("=" * 60)
    
    # Test 1: Backend API Health
    print("\n1. 🔍 Testing Backend API Health...")
    try:
        response = requests.get(f"{backend_url}/", timeout=5)
        if response.status_code == 200:
            print("   ✅ Backend API is healthy")
            print(f"   📝 Response: {response.json()['message']}")
        else:
            print(f"   ❌ Backend API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Backend API is not accessible: {e}")
        return False
    
    # Test 2: Frontend Accessibility
    print("\n2. 🌐 Testing Frontend Accessibility...")
    try:
        response = requests.get(frontend_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ Frontend is accessible")
        else:
            print(f"   ❌ Frontend accessibility failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Frontend is not accessible: {e}")
        return False
    
    # Test 3: Image Prediction
    print("\n3. 🔬 Testing Image Prediction...")
    try:
        test_image = create_test_image()
        files = {'image': ('test_leaf.jpg', test_image, 'image/jpeg')}
        
        response = requests.post(f"{backend_url}/api/predict", files=files, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("   ✅ Image prediction successful")
            print(f"   🌿 Plant: {result.get('realName', 'Unknown')}")
            print(f"   🔬 Scientific: {result.get('scientificName', 'Unknown')}")
            print(f"   📊 Confidence: {result.get('confidence', 0):.1%}")
            print(f"   💊 Primary Use: {result.get('primaryMedicine', 'Unknown')}")
            
            # Test comprehensive data
            if result.get('description'):
                print("   ✅ Comprehensive plant data available")
            if result.get('traditionalSystems'):
                print("   ✅ Traditional medicine systems data available")
            if result.get('preparationMethods'):
                print("   ✅ Preparation methods data available")
            if result.get('safetyInfo'):
                print("   ✅ Safety information available")
            if result.get('geographicalDistribution'):
                print("   ✅ Geographical distribution data available")
                
        else:
            print(f"   ❌ Image prediction failed: {response.status_code}")
            print(f"   📝 Error: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Image prediction error: {e}")
        return False
    
    # Test 4: Data Saving
    print("\n4. 💾 Testing Data Saving...")
    try:
        test_data = {
            "class_name": "test_plant",
            "confidence": 0.85,
            "scientificName": "Testus integrationus",
            "localName": "Integration Test Plant",
            "medicinalFeature": ["test feature", "integration test"]
        }
        
        response = requests.post(f"{backend_url}/api/save", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(test_data),
                               timeout=5)
        if response.status_code == 200:
            print("   ✅ Data saving successful")
            print(f"   📝 Response: {response.json()['message']}")
        else:
            print(f"   ❌ Data saving failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Data saving error: {e}")
        return False
    
    # Test 5: Feedback System
    print("\n5. 📝 Testing Feedback System...")
    try:
        feedback_data = {
            "fileName": "test_integration.jpg",
            "feedback": "Integration test feedback - system working well!",
            "result": {"test": "integration_data"}
        }
        
        response = requests.post(f"{backend_url}/api/feedback", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(feedback_data),
                               timeout=5)
        if response.status_code == 200:
            print("   ✅ Feedback system successful")
            print(f"   📝 Response: {response.json()['message']}")
        else:
            print(f"   ❌ Feedback system failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Feedback system error: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED! System is working correctly!")
    print("=" * 60)
    print(f"\n📱 Frontend URL: {frontend_url}")
    print(f"🔧 Backend API URL: {backend_url}")
    print("\n🚀 Your Medicinal Plant Recognition System is ready to use!")
    
    return True

if __name__ == "__main__":
    success = test_full_integration()
    if not success:
        print("\n❌ Integration test failed. Please check the error messages above.")
        exit(1)
    else:
        print("\n✅ Integration test completed successfully!")
