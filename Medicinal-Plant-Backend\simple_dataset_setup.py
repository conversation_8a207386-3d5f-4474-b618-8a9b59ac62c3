#!/usr/bin/env python3
"""
Simple Dataset Setup for All Medicinal Plants
Works without complex dependencies - just organizes your images properly
"""
import os
import json
import shutil
from pathlib import Path
import random

def create_complete_plant_database():
    """Create comprehensive database for all medicinal plants including Tulsi"""
    
    plants_database = {
        "0": {
            "scientific_name": "Ocimum tenuiflorum",
            "local_name": "Tulsi",
            "real_name": "Holy Basil",
            "common_names": ["Tulsi", "Holy Basil", "Sacred Basil", "Krishna Tulsi"],
            "features": [
                {"name": "Respiratory health", "usage_frequency": "very_high", "description": "Excellent for cough, cold, and breathing problems"},
                {"name": "Immune booster", "usage_frequency": "very_high", "description": "Strengthens immune system naturally"},
                {"name": "Stress relief", "usage_frequency": "high", "description": "Adaptogenic properties reduce stress and anxiety"},
                {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Reduces inflammation throughout the body"}
            ],
            "most_used_medicine": "Respiratory and immune support",
            "description": {
                "appearance": "Small aromatic herb with green or purple leaves, serrated edges",
                "habitat": "Native to India, grows in tropical and subtropical regions",
                "plant_parts_used": ["Leaves", "Seeds", "Whole plant"],
                "active_compounds": ["Eugenol", "Rosmarinic acid", "Oleanolic acid"]
            },
            "traditional_systems": {
                "ayurveda": {
                    "name": "Tulsi",
                    "properties": "Warming, pungent, bitter",
                    "uses": ["Respiratory disorders", "Fever", "Stress relief", "Immune enhancement"]
                }
            },
            "preparation_methods": [
                {
                    "method": "Tea/Decoction",
                    "preparation": "Boil 10-15 fresh leaves in water for 5 minutes",
                    "dosage": "1-2 cups daily",
                    "uses": ["Cough", "Cold", "Stress relief"]
                }
            ],
            "safety_info": {
                "side_effects": ["May lower blood sugar", "Can affect blood clotting"],
                "contraindications": ["Pregnancy (large amounts)", "Before surgery"],
                "warnings": ["Consult doctor if diabetic", "Monitor blood sugar levels"],
                "toxicity_level": "Generally safe when used appropriately"
            },
            "geographical_distribution": {
                "native_regions": ["India", "Southeast Asia"],
                "cultivated_regions": ["India", "Nepal", "Thailand", "Australia"],
                "climate_zones": ["Tropical", "Subtropical"],
                "altitude_range": "Sea level to 2000m"
            }
        },
        "1": {
            "scientific_name": "Curcuma longa",
            "local_name": "Haldi",
            "real_name": "Turmeric",
            "common_names": ["Turmeric", "Haldi", "Golden Spice"],
            "features": [
                {"name": "Anti-inflammatory", "usage_frequency": "very_high", "description": "Powerful natural anti-inflammatory compound"},
                {"name": "Antioxidant", "usage_frequency": "very_high", "description": "Rich in curcumin, strong antioxidant properties"},
                {"name": "Wound healing", "usage_frequency": "high", "description": "Accelerates healing of cuts and wounds"},
                {"name": "Digestive aid", "usage_frequency": "medium", "description": "Helps with digestion and stomach problems"}
            ],
            "most_used_medicine": "Anti-inflammatory and antioxidant treatment",
            "description": {
                "appearance": "Rhizomatous herbaceous perennial plant with bright orange-yellow rhizomes",
                "habitat": "Native to Southeast Asia, requires warm humid climate",
                "plant_parts_used": ["Rhizome", "Root"],
                "active_compounds": ["Curcumin", "Demethoxycurcumin", "Bisdemethoxycurcumin"]
            }
        },
        "2": {
            "scientific_name": "Azadirachta indica",
            "local_name": "Neem",
            "real_name": "Indian Lilac",
            "common_names": ["Neem", "Indian Lilac", "Margosa Tree"],
            "features": [
                {"name": "Antibacterial", "usage_frequency": "very_high", "description": "Natural antibiotic properties"},
                {"name": "Antifungal", "usage_frequency": "very_high", "description": "Fights fungal infections effectively"},
                {"name": "Skin care", "usage_frequency": "high", "description": "Treats various skin conditions"},
                {"name": "Blood purifier", "usage_frequency": "medium", "description": "Detoxifies blood naturally"}
            ],
            "most_used_medicine": "Antibacterial and skin treatment"
        },
        "3": {
            "scientific_name": "Aloe barbadensis miller",
            "local_name": "Aloe Vera",
            "real_name": "True Aloe",
            "common_names": ["Aloe Vera", "Burn Plant", "True Aloe"],
            "features": [
                {"name": "Skin healing", "usage_frequency": "very_high", "description": "Excellent for burns, cuts, and skin irritation"},
                {"name": "Digestive health", "usage_frequency": "high", "description": "Soothes digestive tract and stomach"},
                {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Reduces inflammation and swelling"},
                {"name": "Moisturizing", "usage_frequency": "high", "description": "Natural skin moisturizer"}
            ],
            "most_used_medicine": "Skin healing and digestive support"
        },
        "4": {
            "scientific_name": "Mentha spicata",
            "local_name": "Pudina",
            "real_name": "Mint",
            "common_names": ["Mint", "Pudina", "Spearmint"],
            "features": [
                {"name": "Digestive aid", "usage_frequency": "very_high", "description": "Excellent for stomach problems and indigestion"},
                {"name": "Cooling effect", "usage_frequency": "high", "description": "Natural cooling and refreshing properties"},
                {"name": "Respiratory relief", "usage_frequency": "high", "description": "Helps with congestion and breathing"},
                {"name": "Anti-nausea", "usage_frequency": "medium", "description": "Reduces nausea and motion sickness"}
            ],
            "most_used_medicine": "Digestive and cooling treatment"
        },
        "5": {
            "scientific_name": "Zingiber officinale",
            "local_name": "Adrak",
            "real_name": "Ginger",
            "common_names": ["Ginger", "Adrak", "Fresh Ginger"],
            "features": [
                {"name": "Anti-nausea", "usage_frequency": "very_high", "description": "Highly effective against nausea and vomiting"},
                {"name": "Digestive aid", "usage_frequency": "very_high", "description": "Stimulates digestion and reduces bloating"},
                {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Natural anti-inflammatory properties"},
                {"name": "Immune support", "usage_frequency": "high", "description": "Boosts immune system naturally"}
            ],
            "most_used_medicine": "Digestive and anti-nausea treatment"
        }
    }
    
    return plants_database

def setup_dataset_directories():
    """Create proper dataset directory structure"""
    
    base_dir = Path("data/medicinal_plants")
    train_dir = base_dir / "train"
    val_dir = base_dir / "val"
    test_dir = base_dir / "test"
    
    # Plant names corresponding to the database
    plant_names = ["tulsi", "turmeric", "neem", "aloe_vera", "mint", "ginger"]
    
    print("🏗️ Creating dataset directories...")
    
    # Create directories for each plant
    for plant_name in plant_names:
        for split_dir in [train_dir, val_dir, test_dir]:
            plant_dir = split_dir / plant_name
            plant_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ Created directories for {len(plant_names)} plants")
    return base_dir, plant_names

def save_updated_classes(plants_database):
    """Save the updated classes.json with all plants"""
    
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    classes_file = models_dir / "classes.json"
    
    with open(classes_file, 'w', encoding='utf-8') as f:
        json.dump(plants_database, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Updated classes database: {classes_file}")
    print(f"   📊 Total plants: {len(plants_database)}")

def create_instructions():
    """Create detailed instructions for dataset setup"""
    
    instructions = """
# 🌿 Complete Medicinal Plant Dataset Setup

## 📁 Directory Structure Created:
```
data/medicinal_plants/
├── train/
│   ├── tulsi/          # Add your Tulsi images here (70% of total)
│   ├── turmeric/       # Add Turmeric images here
│   ├── neem/           # Add Neem images here
│   ├── aloe_vera/      # Add Aloe Vera images here
│   ├── mint/           # Add Mint images here
│   └── ginger/         # Add Ginger images here
├── val/                # Validation images (20% of total)
└── test/               # Test images (10% of total)
```

## 🚀 How to Add Your Plant Images:

### Step 1: Collect Images
- **Minimum 50 images per plant** for good accuracy
- **Recommended 100+ images per plant** for excellent accuracy
- Take photos from different angles: top, side, close-up
- Include different parts: leaves, stems, flowers, whole plant
- Use good lighting and clear focus
- Various backgrounds and conditions

### Step 2: Organize Images
```bash
# Method 1: Use the helper script
python add_plant_images.py --add tulsi /path/to/your/tulsi/images
python add_plant_images.py --add turmeric /path/to/your/turmeric/images

# Method 2: Manual organization
# Copy your images to the appropriate folders:
# - 70% to train/plant_name/
# - 20% to val/plant_name/  
# - 10% to test/plant_name/
```

### Step 3: Train the Model
```bash
# Update the training script to use new dataset
python train_transfer.py --data-dir data/medicinal_plants --epochs 50
```

## 📸 Image Guidelines for Each Plant:

### Tulsi (Holy Basil):
- ✅ Close-up of serrated leaves
- ✅ Purple and green varieties
- ✅ Flower spikes if available
- ✅ Different lighting conditions

### Turmeric:
- ✅ Fresh rhizomes (orange/yellow)
- ✅ Cut pieces showing interior
- ✅ Dried turmeric powder
- ✅ Growing plants with leaves

### Neem:
- ✅ Compound leaves (multiple leaflets)
- ✅ Tree bark and branches
- ✅ Flowers and fruits if available
- ✅ Different leaf arrangements

### Aloe Vera:
- ✅ Thick succulent leaves
- ✅ Cut leaves showing gel
- ✅ Whole plant structure
- ✅ Different sizes and ages

### Mint:
- ✅ Serrated oval leaves
- ✅ Different mint varieties
- ✅ Stems and leaf arrangements
- ✅ Fresh vs dried leaves

### Ginger:
- ✅ Fresh ginger rhizomes
- ✅ Cut pieces showing interior
- ✅ Growing plants with leaves
- ✅ Different varieties and sizes

## 🎯 Expected Results:
With proper dataset (100+ images per plant):
- **Tulsi will be correctly identified as "Tulsi" not "Turmeric"**
- **High accuracy (>90%) for all plants**
- **Proper medicinal information displayed**
- **Confidence scores >80%**

## 🔧 Quick Commands:
```bash
# Setup dataset structure
python simple_dataset_setup.py

# Check current dataset status
python add_plant_images.py --summary

# Add images for a specific plant
python add_plant_images.py --add tulsi /path/to/tulsi/folder

# Train the model (after adding images)
python train_transfer.py --data-dir data/medicinal_plants --epochs 30
```

## ⚠️ Important Notes:
1. **Quality over Quantity**: 50 good images are better than 200 poor images
2. **Diversity**: Include different angles, lighting, and backgrounds
3. **Clear Focus**: Avoid blurry or out-of-focus images
4. **Single Plant**: Each image should contain only one type of plant
5. **Natural Colors**: Avoid heavily filtered or edited photos

## 🎉 Success Criteria:
Your model is working when:
- ✅ Tulsi images are identified as "Tulsi" (not Turmeric)
- ✅ Each plant has correct scientific name
- ✅ Confidence scores are >80%
- ✅ Medicinal properties are accurate
"""
    
    with open("COMPLETE_SETUP_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Created complete setup guide: COMPLETE_SETUP_GUIDE.md")

def main():
    print("🌿 Complete Medicinal Plant Dataset Setup")
    print("=" * 50)
    
    # Create plant database
    plants_database = create_complete_plant_database()
    
    # Setup directories
    base_dir, plant_names = setup_dataset_directories()
    
    # Save updated classes
    save_updated_classes(plants_database)
    
    # Create instructions
    create_instructions()
    
    print("\n🎯 Next Steps:")
    print("1. Add your plant images to the created directories")
    print("2. Use: python add_plant_images.py --add tulsi /path/to/tulsi/images")
    print("3. Train model: python train_transfer.py --data-dir data/medicinal_plants")
    print("4. Test with your Tulsi image!")
    
    print(f"\n📁 Dataset directory created: {base_dir}")
    print(f"📋 Plants configured: {', '.join(plant_names)}")
    print("📖 See COMPLETE_SETUP_GUIDE.md for detailed instructions")

if __name__ == "__main__":
    main()
