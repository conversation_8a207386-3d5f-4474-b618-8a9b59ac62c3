import numpy as np
import tensorflow
from tensorflow.keras.preprocessing.image import img_to_array, load_img
from tensorflow.keras.models import load_model
from tensorflow import expand_dims
from tensorflow.nn import sigmoid

# Class Names
class_names = ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>_weed','<PERSON><PERSON><PERSON>','<PERSON>oon_Vine','<PERSON><PERSON><PERSON>','<PERSON>s','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>nt<PERSON>','<PERSON><PERSON><PERSON>','Chilly','Citron lime (herelikai)','Coffee','Common rue(naagdalli)','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','Drumstick','<PERSON><PERSON>','<PERSON>ucaly<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','Globe Amarnath','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Hibis<PERSON>','<PERSON><PERSON>','Insulin','Jackf<PERSON>','<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>_<PERSON><PERSON>','<PERSON><PERSON>_Spinach','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>mbu','<PERSON><PERSON>e','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>dri','<PERSON><PERSON>(<PERSON><PERSON>)','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>ge','<PERSON><PERSON>a','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>pala','<PERSON><PERSON>1','<PERSON><PERSON>nd','<PERSON><PERSON>','<PERSON>coma','Thumbe','Tomato','Tulsi','Turmeric','ashoka','camphor','kamakasturi','kepala']

# Load the model
model = load_model('model_path')
# Load the image to predict
image = load_img('image_path', target_size=(299, 299))
image_array = img_to_array(image)
image_array = expand_dims(image_array, 0)
predictions = model.predict(image_array)
score = sigmoid(predictions[0])
# Predict along with it confidence
print("This image most likely belongs to {} with a {:.2f} percent confidence."
    .format(class_names[np.argmax(score)], 100 * np.max(score)))
