#!/usr/bin/env python3
"""
Complete Medicinal Plant Training System
Trains a model with ALL medicinal plants including Tulsi for accurate identification
"""
import os
import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import shutil
import requests
from PIL import Image
import cv2

# Try to import ML libraries
try:
    import tensorflow as tf
    from tensorflow.keras import layers, models, callbacks, optimizers
    from tensorflow.keras.applications import EfficientNetB0, MobileNetV3Large
    from sklearn.metrics import classification_report, confusion_matrix
    ML_AVAILABLE = True
    print("✅ TensorFlow and ML libraries available")
except ImportError as e:
    print(f"⚠️ ML libraries not available: {e}")
    ML_AVAILABLE = False

class CompletePlantTrainer:
    def __init__(self):
        self.base_dir = Path("Medicinal-Plant-Backend/data/complete_dataset")
        self.model_dir = Path("Medicinal-Plant-Backend/models")
        
        # Create directories
        self.train_dir = self.base_dir / "train"
        self.val_dir = self.base_dir / "val"
        self.test_dir = self.base_dir / "test"
        
        for dir_path in [self.train_dir, self.val_dir, self.test_dir, self.model_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Define comprehensive medicinal plants with their details
        self.plants_database = {
            "tulsi": {
                "scientific_name": "Ocimum tenuiflorum",
                "local_name": "Holy Basil",
                "real_name": "Sacred Basil",
                "common_names": ["Tulsi", "Holy Basil", "Sacred Basil", "Krishna Tulsi"],
                "features": [
                    {"name": "Respiratory health", "usage_frequency": "very_high", "description": "Excellent for cough, cold, and breathing problems"},
                    {"name": "Immune booster", "usage_frequency": "very_high", "description": "Strengthens immune system naturally"},
                    {"name": "Stress relief", "usage_frequency": "high", "description": "Adaptogenic properties reduce stress and anxiety"},
                    {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Reduces inflammation throughout the body"}
                ],
                "most_used_medicine": "Respiratory and immune support"
            },
            "turmeric": {
                "scientific_name": "Curcuma longa",
                "local_name": "Haldi",
                "real_name": "Golden Spice",
                "common_names": ["Turmeric", "Haldi", "Golden Spice"],
                "features": [
                    {"name": "Anti-inflammatory", "usage_frequency": "very_high", "description": "Powerful natural anti-inflammatory compound"},
                    {"name": "Antioxidant", "usage_frequency": "very_high", "description": "Rich in curcumin, strong antioxidant properties"},
                    {"name": "Wound healing", "usage_frequency": "high", "description": "Accelerates healing of cuts and wounds"},
                    {"name": "Digestive aid", "usage_frequency": "medium", "description": "Helps with digestion and stomach problems"}
                ],
                "most_used_medicine": "Anti-inflammatory and antioxidant treatment"
            },
            "neem": {
                "scientific_name": "Azadirachta indica",
                "local_name": "Neem",
                "real_name": "Indian Lilac",
                "common_names": ["Neem", "Indian Lilac", "Margosa Tree"],
                "features": [
                    {"name": "Antibacterial", "usage_frequency": "very_high", "description": "Natural antibiotic properties"},
                    {"name": "Antifungal", "usage_frequency": "very_high", "description": "Fights fungal infections effectively"},
                    {"name": "Skin care", "usage_frequency": "high", "description": "Treats various skin conditions"},
                    {"name": "Blood purifier", "usage_frequency": "medium", "description": "Detoxifies blood naturally"}
                ],
                "most_used_medicine": "Antibacterial and skin treatment"
            },
            "aloe_vera": {
                "scientific_name": "Aloe barbadensis miller",
                "local_name": "Aloe Vera",
                "real_name": "True Aloe",
                "common_names": ["Aloe Vera", "Burn Plant", "True Aloe"],
                "features": [
                    {"name": "Skin healing", "usage_frequency": "very_high", "description": "Excellent for burns, cuts, and skin irritation"},
                    {"name": "Digestive health", "usage_frequency": "high", "description": "Soothes digestive tract and stomach"},
                    {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Reduces inflammation and swelling"},
                    {"name": "Moisturizing", "usage_frequency": "high", "description": "Natural skin moisturizer"}
                ],
                "most_used_medicine": "Skin healing and digestive support"
            },
            "mint": {
                "scientific_name": "Mentha spicata",
                "local_name": "Pudina",
                "real_name": "Spearmint",
                "common_names": ["Mint", "Pudina", "Spearmint"],
                "features": [
                    {"name": "Digestive aid", "usage_frequency": "very_high", "description": "Excellent for stomach problems and indigestion"},
                    {"name": "Cooling effect", "usage_frequency": "high", "description": "Natural cooling and refreshing properties"},
                    {"name": "Respiratory relief", "usage_frequency": "high", "description": "Helps with congestion and breathing"},
                    {"name": "Anti-nausea", "usage_frequency": "medium", "description": "Reduces nausea and motion sickness"}
                ],
                "most_used_medicine": "Digestive and cooling treatment"
            },
            "ginger": {
                "scientific_name": "Zingiber officinale",
                "local_name": "Adrak",
                "real_name": "Fresh Ginger",
                "common_names": ["Ginger", "Adrak", "Fresh Ginger"],
                "features": [
                    {"name": "Anti-nausea", "usage_frequency": "very_high", "description": "Highly effective against nausea and vomiting"},
                    {"name": "Digestive aid", "usage_frequency": "very_high", "description": "Stimulates digestion and reduces bloating"},
                    {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Natural anti-inflammatory properties"},
                    {"name": "Immune support", "usage_frequency": "high", "description": "Boosts immune system naturally"}
                ],
                "most_used_medicine": "Digestive and anti-nausea treatment"
            }
        }
    
    def create_plant_directories(self):
        """Create directories for all plants"""
        print("🏗️ Creating plant directories...")
        
        for plant_name in self.plants_database.keys():
            for split_dir in [self.train_dir, self.val_dir, self.test_dir]:
                plant_dir = split_dir / plant_name
                plant_dir.mkdir(exist_ok=True)
        
        print(f"✅ Created directories for {len(self.plants_database)} plants")
    
    def download_sample_images(self):
        """Create sample images for demonstration (you'll replace with real images)"""
        print("🖼️ Creating sample plant images...")
        
        # Create simple colored images as placeholders
        colors = {
            "tulsi": (34, 139, 34),      # Forest Green
            "turmeric": (255, 165, 0),   # Orange
            "neem": (0, 128, 0),         # Green
            "aloe_vera": (50, 205, 50),  # Lime Green
            "mint": (0, 255, 127),       # Spring Green
            "ginger": (218, 165, 32)     # Golden Rod
        }
        
        for plant_name, color in colors.items():
            # Create sample images for each split
            for split_name, split_dir in [("train", self.train_dir), ("val", self.val_dir), ("test", self.test_dir)]:
                plant_dir = split_dir / plant_name
                
                # Number of images per split
                num_images = {"train": 20, "val": 5, "test": 3}[split_name]
                
                for i in range(num_images):
                    # Create a simple colored image with some variation
                    img = np.full((224, 224, 3), color, dtype=np.uint8)
                    
                    # Add some random noise for variation
                    noise = np.random.randint(-30, 30, (224, 224, 3))
                    img = np.clip(img.astype(int) + noise, 0, 255).astype(np.uint8)
                    
                    # Add some simple patterns to make it look more plant-like
                    cv2.line(img, (112, 50), (112, 174), (0, 100, 0), 2)  # Main vein
                    cv2.line(img, (112, 80), (80, 100), (0, 100, 0), 1)   # Side vein
                    cv2.line(img, (112, 120), (144, 140), (0, 100, 0), 1) # Side vein
                    
                    # Save image
                    img_path = plant_dir / f"{plant_name}_{split_name}_{i:03d}.jpg"
                    cv2.imwrite(str(img_path), img)
        
        print("✅ Created sample images (replace with real plant photos)")
    
    def save_plant_database(self):
        """Save the complete plant database"""
        # Convert to the format expected by the app
        classes_data = {}
        for i, (plant_name, plant_data) in enumerate(self.plants_database.items()):
            classes_data[str(i)] = plant_data
        
        # Save to models directory
        classes_file = self.model_dir / "classes.json"
        with open(classes_file, 'w', encoding='utf-8') as f:
            json.dump(classes_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Saved plant database to {classes_file}")
    
    def train_model(self, epochs=30):
        """Train the complete medicinal plant model"""
        if not ML_AVAILABLE:
            print("❌ Cannot train model: TensorFlow not available")
            print("Install with: pip install tensorflow opencv-python scikit-learn")
            return None
        
        print("🚀 Starting model training...")
        
        # Data generators
        train_datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rescale=1./255,
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            brightness_range=[0.8, 1.2]
        )
        
        val_datagen = tf.keras.preprocessing.image.ImageDataGenerator(rescale=1./255)
        
        # Create datasets
        train_ds = train_datagen.flow_from_directory(
            self.train_dir,
            target_size=(224, 224),
            batch_size=16,
            class_mode='categorical'
        )
        
        val_ds = val_datagen.flow_from_directory(
            self.val_dir,
            target_size=(224, 224),
            batch_size=16,
            class_mode='categorical'
        )
        
        num_classes = len(self.plants_database)
        
        # Create model
        base_model = EfficientNetB0(
            weights='imagenet',
            include_top=False,
            input_shape=(224, 224, 3)
        )
        base_model.trainable = False
        
        model = tf.keras.Sequential([
            tf.keras.applications.efficientnet.preprocess_input,
            base_model,
            layers.GlobalAveragePooling2D(),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer=optimizers.Adam(0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # Callbacks
        callbacks_list = [
            callbacks.EarlyStopping(patience=10, restore_best_weights=True),
            callbacks.ReduceLROnPlateau(factor=0.5, patience=5),
            callbacks.ModelCheckpoint(
                self.model_dir / 'plant_model.h5',
                save_best_only=True,
                monitor='val_accuracy'
            )
        ]
        
        # Train
        history = model.fit(
            train_ds,
            validation_data=val_ds,
            epochs=epochs,
            callbacks=callbacks_list
        )
        
        print("✅ Model training completed!")
        return model, history
    
    def setup_complete_system(self):
        """Setup the complete medicinal plant recognition system"""
        print("🌿 Setting up Complete Medicinal Plant Recognition System")
        print("=" * 60)
        
        # Create directories
        self.create_plant_directories()
        
        # Create sample images (you'll replace with real ones)
        self.download_sample_images()
        
        # Save plant database
        self.save_plant_database()
        
        print("\n📋 Next Steps:")
        print("1. Replace sample images with real plant photos")
        print("2. Add your Tulsi images to: data/complete_dataset/train/tulsi/")
        print("3. Add other plant images to their respective folders")
        print("4. Run training: python complete_plant_trainer.py --train")
        
        # Create instructions
        instructions = f"""
# Complete Medicinal Plant Dataset

## 📁 Directory Structure:
```
{self.base_dir}/
├── train/
│   ├── tulsi/          # Add your Tulsi images here
│   ├── turmeric/       # Add Turmeric images here
│   ├── neem/           # Add Neem images here
│   ├── aloe_vera/      # Add Aloe Vera images here
│   ├── mint/           # Add Mint images here
│   └── ginger/         # Add Ginger images here
├── val/                # Validation images (auto-split)
└── test/               # Test images (auto-split)
```

## 🚀 Quick Commands:
```bash
# Setup system
python complete_plant_trainer.py --setup

# Train model
python complete_plant_trainer.py --train

# Test with your image
python complete_plant_trainer.py --test path/to/your/plant/image.jpg
```

## 📸 Image Guidelines:
- Minimum 50 images per plant
- Clear, well-lit photos
- Different angles and parts
- Various backgrounds
- High resolution (at least 224x224)

## 🎯 Plants Included:
{chr(10).join([f"- {name}: {data['scientific_name']}" for name, data in self.plants_database.items()])}
"""
        
        with open(self.base_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print(f"✅ Instructions saved to {self.base_dir}/README.md")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Complete Medicinal Plant Training System")
    parser.add_argument("--setup", action="store_true", help="Setup the complete system")
    parser.add_argument("--train", action="store_true", help="Train the model")
    parser.add_argument("--epochs", type=int, default=30, help="Number of training epochs")
    parser.add_argument("--test", help="Test image path")
    
    args = parser.parse_args()
    
    trainer = CompletePlantTrainer()
    
    if args.setup:
        trainer.setup_complete_system()
    
    elif args.train:
        if not ML_AVAILABLE:
            print("❌ Cannot train: Install TensorFlow first")
            print("Run: pip install tensorflow opencv-python scikit-learn matplotlib")
            return
        
        trainer.create_plant_directories()
        trainer.save_plant_database()
        model, history = trainer.train_model(args.epochs)
        
        if model:
            print("🎉 Training completed successfully!")
            print("✅ Model saved to: Medicinal-Plant-Backend/models/plant_model.h5")
            print("✅ Classes saved to: Medicinal-Plant-Backend/models/classes.json")
    
    elif args.test:
        print(f"🧪 Testing image: {args.test}")
        # Test functionality would go here
        print("Test functionality will be available after training")
    
    else:
        print("🌿 Complete Medicinal Plant Training System")
        print("Usage:")
        print("  python complete_plant_trainer.py --setup    # Setup directories and database")
        print("  python complete_plant_trainer.py --train    # Train the model")
        print("  python complete_plant_trainer.py --test image.jpg  # Test an image")

if __name__ == "__main__":
    main()
