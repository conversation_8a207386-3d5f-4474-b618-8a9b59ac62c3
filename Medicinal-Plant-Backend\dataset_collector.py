#!/usr/bin/env python3
"""
Dataset Collection System for Medicinal Plants
Helps collect images from multiple angles and organize them properly
"""
import os
import shutil
from pathlib import Path
import json
from datetime import datetime
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import argparse

class DatasetCollector:
    def __init__(self, base_dir="data/plants_dataset"):
        self.base_dir = Path(base_dir)
        self.train_dir = self.base_dir / "train"
        self.val_dir = self.base_dir / "val"
        self.test_dir = self.base_dir / "test"
        
        # Create directories
        for dir_path in [self.train_dir, self.val_dir, self.test_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def create_plant_class(self, plant_name, scientific_name=None):
        """Create directories for a new plant class"""
        plant_name = plant_name.lower().replace(" ", "_")
        
        for split_dir in [self.train_dir, self.val_dir, self.test_dir]:
            plant_dir = split_dir / plant_name
            plant_dir.mkdir(exist_ok=True)
            
        # Create metadata file
        metadata = {
            "plant_name": plant_name,
            "scientific_name": scientific_name or "",
            "created_date": datetime.now().isoformat(),
            "image_count": 0,
            "angles_collected": []
        }
        
        metadata_file = self.base_dir / f"{plant_name}_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"✅ Created plant class: {plant_name}")
        return plant_name
    
    def add_images_from_directory(self, source_dir, plant_name, split_ratio=(0.7, 0.2, 0.1)):
        """Add images from a directory to the dataset with train/val/test split"""
        source_path = Path(source_dir)
        if not source_path.exists():
            print(f"❌ Source directory not found: {source_dir}")
            return
        
        plant_name = plant_name.lower().replace(" ", "_")
        
        # Get all image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_files = [f for f in source_path.iterdir() 
                      if f.suffix.lower() in image_extensions]
        
        if not image_files:
            print(f"❌ No image files found in {source_dir}")
            return
        
        # Shuffle and split
        np.random.shuffle(image_files)
        n_total = len(image_files)
        n_train = int(n_total * split_ratio[0])
        n_val = int(n_total * split_ratio[1])
        
        train_files = image_files[:n_train]
        val_files = image_files[n_train:n_train + n_val]
        test_files = image_files[n_train + n_val:]
        
        # Copy files to appropriate directories
        splits = [
            (train_files, self.train_dir / plant_name, "train"),
            (val_files, self.val_dir / plant_name, "val"),
            (test_files, self.test_dir / plant_name, "test")
        ]
        
        total_copied = 0
        for files, dest_dir, split_name in splits:
            dest_dir.mkdir(exist_ok=True)
            for i, src_file in enumerate(files):
                dest_file = dest_dir / f"{plant_name}_{split_name}_{i:04d}{src_file.suffix}"
                shutil.copy2(src_file, dest_file)
                total_copied += 1
        
        print(f"✅ Added {total_copied} images for {plant_name}")
        print(f"   📊 Split: {len(train_files)} train, {len(val_files)} val, {len(test_files)} test")
        
        return total_copied
    
    def augment_dataset(self, plant_name, target_count=200):
        """Augment dataset for a specific plant to reach target count"""
        plant_name = plant_name.lower().replace(" ", "_")
        train_dir = self.train_dir / plant_name
        
        if not train_dir.exists():
            print(f"❌ Plant directory not found: {train_dir}")
            return
        
        existing_images = list(train_dir.glob("*.jpg")) + list(train_dir.glob("*.png"))
        current_count = len(existing_images)
        
        if current_count >= target_count:
            print(f"✅ {plant_name} already has {current_count} images (target: {target_count})")
            return
        
        needed = target_count - current_count
        print(f"🔄 Augmenting {plant_name}: {current_count} → {target_count} images")
        
        augmented_count = 0
        for i in range(needed):
            # Select random source image
            src_img_path = np.random.choice(existing_images)
            img = cv2.imread(str(src_img_path))
            
            # Apply random augmentation
            augmented_img = self._apply_augmentation(img)
            
            # Save augmented image
            output_path = train_dir / f"{plant_name}_aug_{augmented_count:04d}.jpg"
            cv2.imwrite(str(output_path), augmented_img)
            augmented_count += 1
        
        print(f"✅ Generated {augmented_count} augmented images for {plant_name}")
    
    def _apply_augmentation(self, img):
        """Apply random augmentation to an image"""
        # Convert to PIL for some operations
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        
        # Random rotation (-15 to 15 degrees)
        angle = np.random.uniform(-15, 15)
        pil_img = pil_img.rotate(angle, expand=True, fillcolor=(255, 255, 255))
        
        # Random brightness (0.8 to 1.2)
        brightness = np.random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Brightness(pil_img)
        pil_img = enhancer.enhance(brightness)
        
        # Random contrast (0.8 to 1.2)
        contrast = np.random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Contrast(pil_img)
        pil_img = enhancer.enhance(contrast)
        
        # Random blur (sometimes)
        if np.random.random() < 0.3:
            pil_img = pil_img.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        # Convert back to OpenCV format
        img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        
        # Random horizontal flip
        if np.random.random() < 0.5:
            img = cv2.flip(img, 1)
        
        # Random crop and resize
        h, w = img.shape[:2]
        crop_size = int(min(h, w) * np.random.uniform(0.8, 1.0))
        start_x = np.random.randint(0, w - crop_size + 1)
        start_y = np.random.randint(0, h - crop_size + 1)
        
        cropped = img[start_y:start_y + crop_size, start_x:start_x + crop_size]
        resized = cv2.resize(cropped, (224, 224))
        
        return resized
    
    def get_dataset_summary(self):
        """Get summary of current dataset"""
        summary = {"plants": {}, "total_images": 0}
        
        for plant_dir in self.train_dir.iterdir():
            if plant_dir.is_dir():
                plant_name = plant_dir.name
                train_count = len(list(plant_dir.glob("*.jpg")) + list(plant_dir.glob("*.png")))
                val_count = len(list((self.val_dir / plant_name).glob("*.jpg")) + 
                              list((self.val_dir / plant_name).glob("*.png")))
                test_count = len(list((self.test_dir / plant_name).glob("*.jpg")) + 
                               list((self.test_dir / plant_name).glob("*.png")))
                
                total = train_count + val_count + test_count
                summary["plants"][plant_name] = {
                    "train": train_count,
                    "val": val_count,
                    "test": test_count,
                    "total": total
                }
                summary["total_images"] += total
        
        return summary

def main():
    parser = argparse.ArgumentParser(description="Medicinal Plant Dataset Collector")
    parser.add_argument("--action", choices=["create", "add", "augment", "summary"], 
                       required=True, help="Action to perform")
    parser.add_argument("--plant-name", help="Name of the plant")
    parser.add_argument("--scientific-name", help="Scientific name of the plant")
    parser.add_argument("--source-dir", help="Source directory containing images")
    parser.add_argument("--target-count", type=int, default=200, 
                       help="Target number of images for augmentation")
    
    args = parser.parse_args()
    
    collector = DatasetCollector()
    
    if args.action == "create":
        if not args.plant_name:
            print("❌ --plant-name is required for create action")
            return
        collector.create_plant_class(args.plant_name, args.scientific_name)
    
    elif args.action == "add":
        if not args.plant_name or not args.source_dir:
            print("❌ --plant-name and --source-dir are required for add action")
            return
        collector.add_images_from_directory(args.source_dir, args.plant_name)
    
    elif args.action == "augment":
        if not args.plant_name:
            print("❌ --plant-name is required for augment action")
            return
        collector.augment_dataset(args.plant_name, args.target_count)
    
    elif args.action == "summary":
        summary = collector.get_dataset_summary()
        print("\n📊 Dataset Summary:")
        print("=" * 50)
        for plant, counts in summary["plants"].items():
            print(f"{plant:20} | Train: {counts['train']:3d} | Val: {counts['val']:3d} | Test: {counts['test']:3d} | Total: {counts['total']:3d}")
        print("=" * 50)
        print(f"Total Images: {summary['total_images']}")
        print(f"Total Plants: {len(summary['plants'])}")

if __name__ == "__main__":
    main()
