#!/usr/bin/env python3
"""
Simple script to add your real plant images to the training dataset
"""
import os
import shutil
from pathlib import Path
import argparse
from PIL import Image
import random

def add_images_to_dataset(source_folder, plant_name, dataset_dir="data/medicinal_plants"):
    """Add images from source folder to the dataset with train/val/test split"""
    
    dataset_path = Path(dataset_dir)
    source_path = Path(source_folder)
    
    if not source_path.exists():
        print(f"❌ Source folder not found: {source_folder}")
        return
    
    # Get all image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(source_path.glob(f"*{ext}"))
        image_files.extend(source_path.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print(f"❌ No image files found in {source_folder}")
        return
    
    print(f"📸 Found {len(image_files)} images for {plant_name}")
    
    # Create plant directories if they don't exist
    train_dir = dataset_path / "train" / plant_name
    val_dir = dataset_path / "val" / plant_name
    test_dir = dataset_path / "test" / plant_name
    
    for dir_path in [train_dir, val_dir, test_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # Shuffle and split images (70% train, 20% val, 10% test)
    random.shuffle(image_files)
    total = len(image_files)
    train_count = int(total * 0.7)
    val_count = int(total * 0.2)
    
    train_files = image_files[:train_count]
    val_files = image_files[train_count:train_count + val_count]
    test_files = image_files[train_count + val_count:]
    
    # Copy files to appropriate directories
    def copy_and_resize_images(files, dest_dir, split_name):
        copied = 0
        for i, src_file in enumerate(files):
            try:
                # Open and resize image
                with Image.open(src_file) as img:
                    # Convert to RGB if necessary
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Resize to 224x224 for training
                    img = img.resize((224, 224), Image.Resampling.LANCZOS)
                    
                    # Save with new name
                    dest_file = dest_dir / f"{plant_name}_{split_name}_{i:04d}.jpg"
                    img.save(dest_file, 'JPEG', quality=95)
                    copied += 1
                    
            except Exception as e:
                print(f"⚠️ Error processing {src_file}: {e}")
        
        return copied
    
    # Copy images to each split
    train_copied = copy_and_resize_images(train_files, train_dir, "train")
    val_copied = copy_and_resize_images(val_files, val_dir, "val")
    test_copied = copy_and_resize_images(test_files, test_dir, "test")
    
    total_copied = train_copied + val_copied + test_copied
    
    print(f"✅ Successfully added {total_copied} images for {plant_name}")
    print(f"   📊 Split: {train_copied} train, {val_copied} val, {test_copied} test")
    
    return total_copied

def get_dataset_summary(dataset_dir="data/medicinal_plants"):
    """Get summary of current dataset"""
    dataset_path = Path(dataset_dir)
    train_dir = dataset_path / "train"
    
    if not train_dir.exists():
        print("❌ Dataset directory not found. Run --setup first.")
        return
    
    print("\n📊 Dataset Summary:")
    print("=" * 50)
    
    total_images = 0
    for plant_dir in train_dir.iterdir():
        if plant_dir.is_dir():
            plant_name = plant_dir.name
            
            train_count = len(list(plant_dir.glob("*.jpg")))
            val_count = len(list((dataset_path / "val" / plant_name).glob("*.jpg")))
            test_count = len(list((dataset_path / "test" / plant_name).glob("*.jpg")))
            
            plant_total = train_count + val_count + test_count
            total_images += plant_total
            
            status = "✅ Ready" if plant_total >= 20 else "⚠️ Need more images"
            print(f"{plant_name:15} | Train: {train_count:3d} | Val: {val_count:3d} | Test: {test_count:3d} | Total: {plant_total:3d} | {status}")
    
    print("=" * 50)
    print(f"Total Images: {total_images}")
    print(f"Ready for training: {'✅ Yes' if total_images >= 100 else '❌ Need more images'}")

def main():
    parser = argparse.ArgumentParser(description="Add plant images to training dataset")
    parser.add_argument("--add", nargs=2, metavar=("PLANT_NAME", "SOURCE_FOLDER"),
                       help="Add images: --add tulsi /path/to/tulsi/images")
    parser.add_argument("--summary", action="store_true", help="Show dataset summary")
    parser.add_argument("--setup", action="store_true", help="Setup dataset structure")
    
    args = parser.parse_args()
    
    if args.setup:
        # Run the complete setup
        os.system("python complete_plant_trainer.py --setup")
    
    elif args.add:
        plant_name, source_folder = args.add
        add_images_to_dataset(source_folder, plant_name)
        
    elif args.summary:
        get_dataset_summary()
        
    else:
        print("🌿 Plant Image Dataset Manager")
        print("=" * 40)
        print("Usage:")
        print("  python add_plant_images.py --setup")
        print("  python add_plant_images.py --add tulsi /path/to/tulsi/images")
        print("  python add_plant_images.py --add turmeric /path/to/turmeric/images")
        print("  python add_plant_images.py --summary")
        print()
        print("Supported plants:")
        plants = ["tulsi", "turmeric", "neem", "aloe_vera", "mint", "ginger"]
        for plant in plants:
            print(f"  - {plant}")

if __name__ == "__main__":
    main()
