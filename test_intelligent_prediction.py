#!/usr/bin/env python3
"""
Test the new intelligent image analysis system
"""
import requests
import json
from PIL import Image, ImageDraw
import io
import numpy as np

def create_test_images():
    """Create test images with different characteristics"""
    
    test_images = {}
    
    # Create Tulsi-like image (medium green, moderate texture)
    tulsi_img = Image.new('RGB', (300, 300), (80, 120, 60))  # Medium green
    draw = ImageDraw.Draw(tulsi_img)
    # Add some texture (leaf patterns)
    for i in range(0, 300, 20):
        for j in range(0, 300, 20):
            if (i + j) % 40 == 0:
                draw.rectangle([i, j, i+10, j+10], fill=(90, 140, 70))
    
    tulsi_bytes = io.BytesIO()
    tulsi_img.save(tulsi_bytes, format='JPEG')
    test_images['tulsi'] = tulsi_bytes.getvalue()
    
    # Create Turmeric-like image (yellow/orange, low green)
    turmeric_img = Image.new('RGB', (300, 300), (200, 150, 50))  # Yellow/orange
    draw = ImageDraw.Draw(turmeric_img)
    # Add some texture
    for i in range(0, 300, 15):
        for j in range(0, 300, 15):
            if (i + j) % 30 == 0:
                draw.rectangle([i, j, i+8, j+8], fill=(220, 170, 70))
    
    turmeric_bytes = io.BytesIO()
    turmeric_img.save(turmeric_bytes, format='JPEG')
    test_images['turmeric'] = turmeric_bytes.getvalue()
    
    # Create Neem-like image (dark green, complex texture)
    neem_img = Image.new('RGB', (300, 300), (40, 100, 40))  # Dark green
    draw = ImageDraw.Draw(neem_img)
    # Add complex texture
    for i in range(0, 300, 10):
        for j in range(0, 300, 10):
            if (i * j) % 100 < 50:
                draw.rectangle([i, j, i+5, j+5], fill=(50, 120, 50))
    
    neem_bytes = io.BytesIO()
    neem_img.save(neem_bytes, format='JPEG')
    test_images['neem'] = neem_bytes.getvalue()
    
    # Create Mint-like image (bright green, fine texture)
    mint_img = Image.new('RGB', (300, 300), (60, 150, 80))  # Bright green
    draw = ImageDraw.Draw(mint_img)
    # Add fine texture
    for i in range(0, 300, 8):
        for j in range(0, 300, 8):
            if (i + j) % 16 == 0:
                draw.rectangle([i, j, i+4, j+4], fill=(70, 170, 90))
    
    mint_bytes = io.BytesIO()
    mint_img.save(mint_bytes, format='JPEG')
    test_images['mint'] = mint_bytes.getvalue()
    
    return test_images

def test_prediction(image_bytes, expected_plant):
    """Test prediction with image bytes"""
    
    url = "http://localhost:5000/predict"
    
    files = {'file': ('test.jpg', image_bytes, 'image/jpeg')}
    
    try:
        response = requests.post(url, files=files)
        
        if response.status_code == 200:
            result = response.json()
            predicted_plant = result.get('plantName', 'Unknown')
            confidence = result.get('confidence', 0)
            
            print(f"Expected: {expected_plant}")
            print(f"Predicted: {predicted_plant}")
            print(f"Confidence: {confidence:.3f}")
            print(f"Scientific: {result.get('scientificName', 'N/A')}")
            
            # Check if prediction makes sense
            if expected_plant.lower() in predicted_plant.lower():
                print("✅ CORRECT prediction!")
            else:
                print("❌ Incorrect prediction")
            
            return result
        else:
            print(f"❌ Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def main():
    """Test the intelligent prediction system"""
    
    print("🧪 Testing Intelligent Image Analysis System")
    print("=" * 60)
    print("🎯 This tests if the system can distinguish plants by visual features")
    print()
    
    # Create test images
    print("🎨 Creating test images with different characteristics...")
    test_images = create_test_images()
    print(f"✅ Created {len(test_images)} test images")
    print()
    
    # Test each image
    results = {}
    
    for plant_name, image_bytes in test_images.items():
        print(f"🔍 Testing {plant_name.upper()} characteristics:")
        print("-" * 40)
        
        result = test_prediction(image_bytes, plant_name)
        results[plant_name] = result
        print()
    
    # Summary
    print("📊 SUMMARY:")
    print("=" * 60)
    
    correct_predictions = 0
    total_predictions = len(results)
    
    for plant_name, result in results.items():
        if result:
            predicted = result.get('plantName', 'Unknown')
            confidence = result.get('confidence', 0)
            
            if plant_name.lower() in predicted.lower():
                status = "✅ CORRECT"
                correct_predictions += 1
            else:
                status = "❌ INCORRECT"
            
            print(f"{plant_name.upper():10} → {predicted:15} ({confidence:.3f}) {status}")
    
    accuracy = (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    print(f"\n🎯 Accuracy: {correct_predictions}/{total_predictions} ({accuracy:.1f}%)")
    
    if accuracy > 50:
        print("✅ Intelligent analysis is working!")
        print("📋 Much better than random predictions!")
    else:
        print("⚠️ System needs improvement")
    
    print("\n🚀 NEXT STEPS FOR PERFECT ACCURACY:")
    print("1. 📥 Train with real dataset (1000+ images)")
    print("2. 🔧 Use Google Colab for training")
    print("3. 📱 Collect your own plant photos")
    print("4. 🎯 Achieve >95% accuracy!")

if __name__ == "__main__":
    main()
